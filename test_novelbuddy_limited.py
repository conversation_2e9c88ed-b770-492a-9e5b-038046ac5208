#!/usr/bin/env python3
"""
Test script for NovelBuddy provider with limited chapters for EPUB generation testing.
"""

import asyncio
import logging
import sys
from pathlib import Path

# Add the src directory to the path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from wn_dl.config import get_provider_config
from wn_dl.core.processor import NovelProcessor
from wn_dl.providers.novelbuddy import NovelBuddyScraper

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

TEST_NOVEL_URL = "https://novelbuddy.com/novel/my-boss-is-secretly-a-softie"
MAX_CHAPTERS = 2  # Limit to first 2 chapters for testing


async def test_limited_scraping():
    """Test scraping with limited chapters for EPUB generation."""
    logger.info("🚀 Starting Limited NovelBuddy Scraping Test")
    logger.info(f"Test URL: {TEST_NOVEL_URL}")
    logger.info(f"Max chapters: {MAX_CHAPTERS}")

    try:
        # Load configuration
        config = get_provider_config("novelbuddy")

        async with NovelBuddyScraper(config) as scraper:
            # Get novel metadata
            logger.info("=== Getting Novel Metadata ===")
            metadata = await scraper.get_novel_metadata(TEST_NOVEL_URL)
            if not metadata:
                logger.error("❌ Failed to get novel metadata")
                return False

            logger.info(f"✅ Got metadata for: {metadata.title}")

            # Get chapter list
            logger.info("=== Getting Chapter List ===")
            all_chapters = await scraper.get_chapter_list(TEST_NOVEL_URL, metadata)
            if not all_chapters:
                logger.error("❌ Failed to get chapter list")
                return False

            # Limit chapters for testing
            chapters = all_chapters[:MAX_CHAPTERS]
            logger.info(f"✅ Limited to {len(chapters)} chapters for testing")

            # Test scraping chapters
            logger.info("=== Scraping Chapter Content ===")
            scraped_chapters = []

            for i, chapter_info in enumerate(chapters):
                logger.info(
                    f"Scraping chapter {i+1}/{len(chapters)}: {chapter_info['title']}"
                )

                chapter_data = await scraper.scrape_chapter_content(chapter_info["url"])
                if chapter_data:
                    scraped_chapters.append(chapter_data)
                    logger.info(f"   ✅ Success - {chapter_data.word_count} words")
                else:
                    logger.error(f"   ❌ Failed to scrape chapter")

            if not scraped_chapters:
                logger.error("❌ No chapters were successfully scraped")
                return False

            logger.info(f"✅ Successfully scraped {len(scraped_chapters)} chapters")

            # Test using the processor for EPUB generation
            logger.info("=== Testing EPUB Generation ===")
            processor = NovelProcessor()

            # Process the novel for both markdown and EPUB generation
            result = await processor.process_novel(
                TEST_NOVEL_URL,
                output_dir="test_novelbuddy_limited_epub",
                formats=["markdown"],
            )

            if result.get("success"):
                logger.info("✅ EPUB generation completed successfully")
                logger.info(f"   Novel: {result.get('novel_title', 'Unknown')}")
                logger.info(f"   Author: {result.get('author', 'Unknown')}")
                logger.info(f"   Total chapters: {result.get('total_chapters', 0)}")
                logger.info(
                    f"   Successful chapters: {result.get('successful_chapters', 0)}"
                )
                logger.info(f"   Generated files: {result.get('generated_files', [])}")
                logger.info(
                    f"   Output directory: {result.get('output_directory', 'Unknown')}"
                )
                return True
            else:
                logger.error(f"❌ EPUB generation failed: {result.get('error')}")
                return False

    except Exception as e:
        logger.error(f"❌ Test failed with exception: {e}")
        return False


async def main():
    """Main test function."""
    success = await test_limited_scraping()

    if success:
        logger.info("🎉 All tests passed! NovelBuddy provider is working correctly.")
        return 0
    else:
        logger.error("💥 Tests failed!")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
