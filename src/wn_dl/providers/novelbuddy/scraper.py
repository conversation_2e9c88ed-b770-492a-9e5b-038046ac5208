"""
NovelBuddy scraper implementation.

This module provides a concrete scraper for NovelBuddy (novelbuddy.com).
Handles AJAX-based chapter discovery and novel ID extraction.
"""

import asyncio
import json
import logging
import re
from typing import Any, Dict, List, Optional
from urllib.parse import urljoin, urlparse

import aiohttp
from bs4 import BeautifulSoup

from ...core.base_scraper import BaseNovelScraper
from ...core.models import ChapterData, NovelMetadata, NovelStatus

logger = logging.getLogger(__name__)


class NovelBuddyScraper(BaseNovelScraper):
    """
    Scraper implementation for NovelBuddy (novelbuddy.com).

    Handles extraction of novel metadata and chapter content from NovelBuddy pages.
    Uses AJAX for chapter list retrieval and requires novel ID extraction.
    """

    def __init__(self, config: Dict[str, Any], session=None):
        """
        Initialize the NovelBuddy scraper.

        Args:
            config: Configuration dictionary (if empty, will load provider config)
            session: Optional aiohttp session
        """
        # Load provider configuration if not provided
        if not config:
            from ...config import get_provider_config

            try:
                config = get_provider_config("novelbuddy")
            except Exception as e:
                logger.warning(f"Could not load novelbuddy provider config: {e}")
                config = {}

        super().__init__(config, session)

    def get_provider_name(self) -> str:
        """Get the provider name."""
        return "NovelBuddy"

    def get_max_concurrent_requests(self) -> int:
        """
        Get maximum concurrent requests for NovelBuddy.

        Returns:
            Maximum number of concurrent requests
        """
        download_config = self.config.get("chapter_downloading", {})
        return download_config.get("max_concurrent", 2)

    def get_chunk_size(self) -> int:
        """
        Get chunk size for batch processing.

        Returns:
            Number of chapters to process in each chunk
        """
        download_config = self.config.get("chapter_downloading", {})
        return download_config.get("chunk_size", 5)

    def get_delay_between_chunks(self) -> float:
        """
        Get delay between chunks in seconds.

        Returns:
            Delay in seconds between processing chunks
        """
        download_config = self.config.get("chapter_downloading", {})
        return download_config.get("delay_between_chunks", 5.0)

    async def extract_novel_id(self, novel_url: str) -> Optional[str]:
        """
        Extract the numeric novel ID from a NovelBuddy novel page.

        This is critical for AJAX API calls which require the numeric ID.

        Args:
            novel_url: URL of the novel page

        Returns:
            Numeric novel ID as string, or None if extraction failed
        """
        try:
            # Get the novel page HTML
            soup = await self._get_soup(novel_url)
            if not soup:
                logger.error(f"Failed to fetch novel page: {novel_url}")
                return None

            # Method 1: Extract from JavaScript variables
            novel_id = self._extract_id_from_javascript(str(soup))
            if novel_id:
                logger.info(f"Extracted novel ID from JavaScript: {novel_id}")
                return novel_id

            # Method 2: Look for data attributes or hidden inputs
            novel_id = self._extract_id_from_attributes(soup)
            if novel_id:
                logger.info(f"Extracted novel ID from attributes: {novel_id}")
                return novel_id

            # Method 3: Try to find ID in AJAX requests or API calls
            novel_id = self._extract_id_from_ajax_patterns(html_content)
            if novel_id:
                logger.info(f"Extracted novel ID from AJAX patterns: {novel_id}")
                return novel_id

            logger.error(f"Could not extract novel ID from: {novel_url}")
            return None

        except Exception as e:
            logger.error(f"Error extracting novel ID from {novel_url}: {e}")
            return None

    def _extract_id_from_javascript(self, html_content: str) -> Optional[str]:
        """
        Extract novel ID from JavaScript variables in the HTML.

        Args:
            html_content: Raw HTML content of the page

        Returns:
            Novel ID as string or None if not found
        """
        try:
            # Look for bookId variable
            bookid_pattern = r"var\s+bookId\s*=\s*(\d+)"
            match = re.search(bookid_pattern, html_content)
            if match:
                return match.group(1)

            # Look for mangaId variable (alternative)
            mangaid_pattern = r"var\s+mangaId\s*=\s*(\d+)"
            match = re.search(mangaid_pattern, html_content)
            if match:
                return match.group(1)

            # Look for other common patterns
            patterns = [
                r"bookId\s*:\s*(\d+)",
                r"mangaId\s*:\s*(\d+)",
                r'"id"\s*:\s*(\d+)',
                r"novel_id\s*=\s*(\d+)",
                r"manga_id\s*=\s*(\d+)",
            ]

            for pattern in patterns:
                match = re.search(pattern, html_content)
                if match:
                    return match.group(1)

            return None

        except Exception as e:
            logger.error(f"Error extracting ID from JavaScript: {e}")
            return None

    def _parse_novel_status(self, status_text: Optional[str]) -> str:
        """
        Parse novel status from text.

        Args:
            status_text: Raw status text from the page

        Returns:
            Standardized status string
        """
        if not status_text:
            return "Unknown"

        status_text = status_text.lower().strip()

        if "ongoing" in status_text or "updating" in status_text:
            return "Ongoing"
        elif "completed" in status_text or "complete" in status_text:
            return "Completed"
        elif "hiatus" in status_text or "pause" in status_text:
            return "Hiatus"
        elif "dropped" in status_text or "discontinued" in status_text:
            return "Dropped"
        else:
            return status_text.title()

    def _extract_id_from_attributes(self, soup: BeautifulSoup) -> Optional[str]:
        """
        Extract novel ID from HTML attributes or data elements.

        Args:
            soup: BeautifulSoup object of the page

        Returns:
            Novel ID as string or None if not found
        """
        try:
            # Look for data attributes
            for attr in ["data-novel-id", "data-manga-id", "data-book-id", "data-id"]:
                element = soup.find(attrs={attr: True})
                if element:
                    return element.get(attr)

            # Look for hidden inputs
            for name in ["novel_id", "manga_id", "book_id", "id"]:
                input_elem = soup.find("input", {"name": name, "type": "hidden"})
                if input_elem and input_elem.get("value"):
                    return input_elem.get("value")

            # Look for meta tags
            for name in ["novel-id", "manga-id", "book-id"]:
                meta_elem = soup.find("meta", {"name": name})
                if meta_elem and meta_elem.get("content"):
                    return meta_elem.get("content")

            return None

        except Exception as e:
            logger.error(f"Error extracting ID from attributes: {e}")
            return None

    def _extract_id_from_ajax_patterns(self, html_content: str) -> Optional[str]:
        """
        Extract novel ID from AJAX URL patterns in the HTML.

        Args:
            html_content: Raw HTML content of the page

        Returns:
            Novel ID as string or None if not found
        """
        try:
            # Look for API endpoint patterns
            api_patterns = [
                r"/api/manga/(\d+)/chapters",
                r"/api/novel/(\d+)/",
                r"/manga/(\d+)/chapters",
                r'manga_id["\']?\s*:\s*["\']?(\d+)',
                r'novel_id["\']?\s*:\s*["\']?(\d+)',
            ]

            for pattern in api_patterns:
                match = re.search(pattern, html_content)
                if match:
                    return match.group(1)

            return None

        except Exception as e:
            logger.error(f"Error extracting ID from AJAX patterns: {e}")
            return None

    async def _make_ajax_request(
        self,
        url: str,
        headers: Dict[str, str] = None,
        data: Dict[str, Any] = None,
        expected_type: str = "json",
    ) -> Optional[Dict[str, Any]]:
        """
        Make an AJAX request and return the response.

        Args:
            url: URL to make AJAX request to
            headers: Optional headers for the request
            data: Optional data to send in request body
            expected_type: Expected response type ("json" or "html")

        Returns:
            Response as dict or None if request failed
        """
        await self._rate_limit()

        try:
            request_headers = {"User-Agent": self.user_agent}

            # Add AJAX-specific headers from config
            ajax_headers = self.config.get("request", {}).get("ajax_headers", {})
            request_headers.update(ajax_headers)

            if headers:
                request_headers.update(headers)

            async with self.session.get(
                url, headers=request_headers, params=data
            ) as response:
                if response.status == 200:
                    if expected_type == "html":
                        # Expecting HTML response
                        text_content = await response.text()
                        logger.debug(f"Received HTML response from: {url}")
                        return {"html": text_content}
                    else:
                        # Expecting JSON response
                        try:
                            return await response.json()
                        except (json.JSONDecodeError, aiohttp.ContentTypeError):
                            # If not JSON, return text content with warning
                            text_content = await response.text()
                            logger.warning(
                                f"AJAX response was not JSON, got HTML instead: {url}"
                            )
                            return {"html": text_content}
                else:
                    logger.error(
                        f"AJAX request failed with status {response.status}: {url}"
                    )
                    return None

        except Exception as e:
            logger.error(f"Error making AJAX request to {url}: {e}")
            return None

    async def get_novel_metadata(self, novel_url: str) -> Optional[NovelMetadata]:
        """
        Extract novel metadata from NovelBuddy novel page.

        Args:
            novel_url: URL of the novel page

        Returns:
            NovelMetadata object or None if extraction failed
        """
        try:
            # First extract the novel ID - this is critical for NovelBuddy
            novel_id = await self.extract_novel_id(novel_url)
            if not novel_id:
                logger.error(f"Could not extract novel ID from {novel_url}")
                return None

            # Get the novel page HTML
            soup = await self._get_soup(novel_url)
            if not soup:
                return None
            selectors = self.config.get("selectors", {})

            # Extract basic metadata
            title = self._extract_text_by_selector(soup, selectors.get("title", "h1"))
            if not title:
                logger.error(f"Could not extract title from {novel_url}")
                return None

            author = self._extract_text_by_selector(
                soup, selectors.get("author", "p strong")
            )
            description = self._extract_text_by_selector(
                soup, selectors.get("description", "div.section-body.summary p.content")
            )

            # Handle lazy-loaded cover images
            cover_url = self._extract_cover_image(
                soup, selectors.get("cover_image", "img.lazy[data-src]")
            )
            if cover_url:
                cover_url = urljoin(novel_url, cover_url)

            # Extract additional metadata
            genres = self._extract_list_by_selector(
                soup, selectors.get("genres", ".genres-content a")
            )
            tags = self._extract_list_by_selector(
                soup, selectors.get("tags", ".tags-content a")
            )

            status_text = self._extract_text_by_selector(
                soup, selectors.get("status", ".post-status .summary-content")
            )
            status = self._parse_novel_status(status_text)

            # Store novel ID for later use
            metadata = NovelMetadata(
                title=title,
                author=author or "Unknown",
                description=description or "",
                source_url=novel_url,
                cover_url=cover_url,
                genres=genres,
                tags=tags,
                status=status,
                provider="NovelBuddy",
                provider_id=novel_id,  # Store the extracted ID
            )

            logger.info(
                f"Successfully extracted metadata for: {title} (ID: {novel_id})"
            )
            return metadata

        except Exception as e:
            logger.error(f"Error extracting novel metadata from {novel_url}: {e}")
            return None

    def _extract_cover_image(self, soup: BeautifulSoup, selector: str) -> Optional[str]:
        """
        Extract cover image URL, handling lazy-loaded images.

        Args:
            soup: BeautifulSoup object
            selector: CSS selector for cover image

        Returns:
            Cover image URL or None if not found
        """
        try:
            img_elem = soup.select_one(selector)
            if not img_elem:
                return None

            # Check for lazy-loaded image (data-src)
            cover_url = img_elem.get("data-src")
            if cover_url:
                return cover_url

            # Fallback to regular src
            cover_url = img_elem.get("src")
            return cover_url

        except Exception as e:
            logger.error(f"Error extracting cover image: {e}")
            return None

    async def get_chapter_list(
        self, novel_url: str, novel_metadata: NovelMetadata = None
    ) -> List[Dict[str, str]]:
        """
        Get list of chapters using AJAX API.

        Args:
            novel_url: URL of the novel page
            novel_metadata: Optional novel metadata containing novel_id

        Returns:
            List of ChapterData objects
        """
        try:
            # Get novel ID from metadata or extract it
            novel_id = None
            if novel_metadata and hasattr(novel_metadata, "novel_id"):
                novel_id = novel_metadata.novel_id

            if not novel_id:
                novel_id = await self.extract_novel_id(novel_url)

            if not novel_id:
                logger.error(f"Could not get novel ID for chapter list: {novel_url}")
                return []

            # Build AJAX endpoint URL
            discovery_config = self.config.get("chapter_discovery", {})
            ajax_endpoint = discovery_config.get(
                "ajax_endpoint", "/api/manga/{novel_id}/chapters?source=detail"
            )
            ajax_url = ajax_endpoint.format(novel_id=novel_id)

            base_url = self.config.get("provider", {}).get(
                "base_url", "https://novelbuddy.com"
            )
            full_ajax_url = urljoin(base_url, ajax_url)

            logger.info(f"Fetching chapter list from: {full_ajax_url}")

            # Get expected response type from config
            discovery_config = self.config.get("chapter_discovery", {})
            expected_response_type = discovery_config.get("ajax_response_type", "json")

            # Make AJAX request
            response_data = await self._make_ajax_request(
                full_ajax_url, expected_type=expected_response_type
            )
            if not response_data:
                logger.error(f"Failed to get chapter list from AJAX: {full_ajax_url}")
                return []

            # Parse chapter list from response
            chapters = self._parse_ajax_chapter_list(response_data, novel_url)

            logger.info(f"Found {len(chapters)} chapters for novel ID {novel_id}")
            return chapters

        except Exception as e:
            logger.error(f"Error getting chapter list for {novel_url}: {e}")
            return []

    def _parse_ajax_chapter_list(
        self, response_data: Dict[str, Any], novel_url: str
    ) -> List[Dict[str, str]]:
        """
        Parse chapter list from AJAX response.

        Args:
            response_data: JSON response from AJAX request
            novel_url: Base novel URL for building chapter URLs

        Returns:
            List of dictionaries containing chapter info (title, url, number)
        """
        chapters = []

        try:
            # Handle different response formats
            if "html" in response_data:
                # Response contains HTML content
                html_content = response_data["html"]
                soup = BeautifulSoup(html_content, "html.parser")

                # Parse chapter list from HTML
                selectors = self.config.get("selectors", {})
                chapter_elements = soup.select(
                    selectors.get("chapter_list", "ul.chapter-list li")
                )

                for i, chapter_elem in enumerate(chapter_elements):
                    chapter_data = self._parse_chapter_element(
                        chapter_elem, i + 1, novel_url
                    )
                    if chapter_data:
                        chapters.append(chapter_data)

            elif "chapters" in response_data:
                # Response contains chapter data directly
                chapter_list = response_data["chapters"]
                for i, chapter_info in enumerate(chapter_list):
                    chapter_data = self._parse_chapter_json(
                        chapter_info, i + 1, novel_url
                    )
                    if chapter_data:
                        chapters.append(chapter_data)

            elif "html" in response_data:
                # Response contains HTML content - parse the chapter list
                html_content = response_data["html"]
                soup = BeautifulSoup(html_content, "html.parser")

                # Parse chapter list from HTML structure using configured selectors
                selectors = self.config.get("selectors", {})
                chapter_list_selector = selectors.get(
                    "chapter_list", "ul.chapter-list li"
                )

                chapter_elements = soup.select(chapter_list_selector)
                logger.debug(
                    f"Found {len(chapter_elements)} chapter elements using selector: {chapter_list_selector}"
                )

                for i, element in enumerate(chapter_elements):
                    chapter_data = self._parse_chapter_element(
                        element, i + 1, novel_url
                    )
                    if chapter_data:
                        chapters.append(chapter_data)

            else:
                logger.warning(
                    f"Unknown AJAX response format: {list(response_data.keys())}"
                )

        except Exception as e:
            logger.error(f"Error parsing AJAX chapter list: {e}")

        return chapters

    def _parse_chapter_element(
        self, chapter_elem, chapter_number: int, novel_url: str
    ) -> Optional[Dict[str, str]]:
        """
        Parse a single chapter element from HTML.

        Args:
            chapter_elem: BeautifulSoup element containing chapter info
            chapter_number: Chapter number
            novel_url: Base novel URL

        Returns:
            Dictionary containing chapter info or None if parsing failed
        """
        try:
            selectors = self.config.get("selectors", {})

            # Extract chapter link
            link_elem = chapter_elem.select_one(selectors.get("chapter_link", "a"))
            if not link_elem:
                return None

            chapter_url = link_elem.get("href")
            if not chapter_url:
                return None

            # Make URL absolute
            if not chapter_url.startswith("http"):
                base_url = self.config.get("provider", {}).get(
                    "base_url", "https://novelbuddy.com"
                )
                chapter_url = urljoin(base_url, chapter_url)

            # Extract chapter title - try specific selector first, then fallback
            title_elem = link_elem.select_one(".chapter-title")
            if not title_elem:
                title_elem = link_elem.select_one("strong.chapter-title")
            if not title_elem:
                title_elem = link_elem

            chapter_title = (
                title_elem.get_text(strip=True)
                if title_elem
                else f"Chapter {chapter_number}"
            )

            # Extract chapter number from title or URL
            extracted_number = self._extract_chapter_number(chapter_title, chapter_url)
            if extracted_number:
                chapter_number = extracted_number

            return {
                "title": chapter_title,
                "url": chapter_url,
                "number": chapter_number,
            }

        except Exception as e:
            logger.error(f"Error parsing chapter element: {e}")
            return None

    def _parse_chapter_json(
        self, chapter_info: Dict[str, Any], chapter_number: int, novel_url: str
    ) -> Optional[Dict[str, str]]:
        """
        Parse a single chapter from JSON data.

        Args:
            chapter_info: Dictionary containing chapter information
            chapter_number: Chapter number
            novel_url: Base novel URL

        Returns:
            Dictionary containing chapter info or None if parsing failed
        """
        try:
            # Extract chapter URL
            chapter_url = chapter_info.get("url") or chapter_info.get("link")
            if not chapter_url:
                return None

            # Make URL absolute
            if not chapter_url.startswith("http"):
                base_url = self.config.get("provider", {}).get(
                    "base_url", "https://novelbuddy.com"
                )
                chapter_url = urljoin(base_url, chapter_url)

            # Extract chapter title
            chapter_title = (
                chapter_info.get("title")
                or chapter_info.get("name")
                or f"Chapter {chapter_number}"
            )

            return {
                "title": chapter_title,
                "url": chapter_url,
                "number": chapter_number,
            }

        except Exception as e:
            logger.error(f"Error parsing chapter JSON: {e}")
            return None

    async def scrape_chapter_content(self, chapter_url: str) -> Optional[ChapterData]:
        """
        Scrape content from a NovelBuddy chapter page.

        Args:
            chapter_url: URL of the chapter page

        Returns:
            ChapterData object or None if extraction failed
        """
        try:
            soup = await self._get_soup(chapter_url)
            if not soup:
                return None
            selectors = self.config.get("selectors", {})

            # Extract chapter title
            title_selector = selectors.get(
                "chapter_title", "h1, .chapter-title, .entry-title"
            )
            title_elem = soup.select_one(title_selector)
            chapter_title = (
                title_elem.get_text(strip=True) if title_elem else "Untitled Chapter"
            )

            # Extract chapter content
            content_selector = selectors.get(
                "chapter_content", "div.chapter__content div.content-inner"
            )
            content_elem = soup.select_one(content_selector)

            if not content_elem:
                # Try fallback selectors
                fallback_selectors = (
                    self.config.get("error_handling", {})
                    .get("fallback_selectors", {})
                    .get("chapter_content", [])
                )
                for fallback_selector in fallback_selectors:
                    content_elem = soup.select_one(fallback_selector)
                    if content_elem:
                        logger.info(f"Used fallback selector: {fallback_selector}")
                        break

            if not content_elem:
                logger.error(f"Could not find chapter content in: {chapter_url}")
                return None

            # Clean the content
            cleaned_content = self._clean_chapter_content(content_elem)

            if not cleaned_content or len(cleaned_content.strip()) < 50:
                logger.warning(f"Chapter content too short or empty: {chapter_url}")
                return None

            # Extract chapter number from title or URL
            chapter_number = self._extract_chapter_number(chapter_title, chapter_url)

            # Create ChapterData object
            chapter_data = ChapterData(
                title=chapter_title,
                content=cleaned_content,
                url=chapter_url,
                chapter_number=chapter_number,
                is_cleaned=True,
            )

            # Calculate word count
            chapter_data.calculate_word_count()

            logger.debug(f"Successfully extracted content from: {chapter_url}")
            return chapter_data

        except Exception as e:
            logger.error(f"Error scraping chapter content from {chapter_url}: {e}")
            return None

    def _clean_chapter_content(self, content_elem) -> str:
        """
        Clean chapter content by removing unwanted elements and formatting text.

        Args:
            content_elem: BeautifulSoup element containing chapter content

        Returns:
            Cleaned content as string
        """
        try:
            # Remove unwanted elements
            cleaning_config = self.config.get("content_cleaning", {})
            remove_selectors = cleaning_config.get("remove_selectors", [])

            for selector in remove_selectors:
                for elem in content_elem.select(selector):
                    elem.decompose()

            # Extract text content
            text_content = content_elem.get_text(separator="\n", strip=True)

            # Apply text processing
            text_processing = cleaning_config.get("text_processing", {})

            if text_processing.get("remove_html_tags", True):
                # Remove any remaining HTML tags
                import re

                text_content = re.sub(r"<[^>]+>", "", text_content)

            if text_processing.get("normalize_whitespace", True):
                # Normalize whitespace
                text_content = re.sub(r"\s+", " ", text_content)

            if text_processing.get("remove_empty_lines", True):
                # Remove empty lines
                lines = text_content.split("\n")
                lines = [line.strip() for line in lines if line.strip()]
                text_content = "\n".join(lines)

            if text_processing.get("convert_html_entities", True):
                # Convert HTML entities
                import html

                text_content = html.unescape(text_content)

            if text_processing.get("remove_extra_spaces", True):
                # Remove extra spaces
                text_content = re.sub(r" +", " ", text_content)

            # Preserve paragraph breaks if configured
            if text_processing.get("preserve_paragraph_breaks", True):
                # Add double newlines between paragraphs
                text_content = re.sub(r"\n", "\n\n", text_content)
                # Remove triple+ newlines
                text_content = re.sub(r"\n{3,}", "\n\n", text_content)

            return text_content.strip()

        except Exception as e:
            logger.error(f"Error cleaning chapter content: {e}")
            return ""

    def _extract_chapter_number(
        self, chapter_title: str, chapter_url: str
    ) -> Optional[int]:
        """
        Extract chapter number from title or URL.

        Args:
            chapter_title: Chapter title text
            chapter_url: Chapter URL

        Returns:
            Chapter number as integer or None if not found
        """
        import re

        try:
            # Try to extract from title first
            title_patterns = [
                r"chapter\s*(\d+)",
                r"ch\s*(\d+)",
                r"c\s*(\d+)",
                r"episode\s*(\d+)",
                r"part\s*(\d+)",
                r"(\d+)",  # Just a number
            ]

            for pattern in title_patterns:
                match = re.search(pattern, chapter_title.lower())
                if match:
                    return int(match.group(1))

            # Try to extract from URL
            url_patterns = [
                r"/chapter-(\d+)",
                r"/ch-(\d+)",
                r"/c-(\d+)",
                r"/(\d+)/?$",
                r"chapter=(\d+)",
                r"ch=(\d+)",
            ]

            for pattern in url_patterns:
                match = re.search(pattern, chapter_url.lower())
                if match:
                    return int(match.group(1))

            return None

        except (ValueError, AttributeError) as e:
            logger.debug(f"Could not extract chapter number: {e}")
            return None
