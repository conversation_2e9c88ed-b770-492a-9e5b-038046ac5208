"""
Markdown parser for EbookLib EPUB generator.

This module handles parsing of markdown files with YAML frontmatter,
extracting chapters, metadata, and content structure for EPUB generation.
"""

import logging
import re
from dataclasses import dataclass
from pathlib import Path
from typing import Any, Dict, List, Optional

import yaml

logger = logging.getLogger(__name__)


@dataclass
class ParsedChapter:
    """Represents a parsed chapter from markdown."""

    title: str
    content: str
    anchor: str
    chapter_number: int


@dataclass
class ParsedMarkdownData:
    """Represents parsed markdown file data."""

    metadata: Dict[str, Any]
    chapters: List[ParsedChapter]
    title_page_content: Optional[str] = None
    toc_content: Optional[str] = None


class MarkdownParser:
    """
    Parser for markdown files with YAML frontmatter and chapter structure.

    Handles the specific format used by the web novel scraper, including
    YAML metadata, title pages, table of contents, and chapter boundaries.
    """

    def __init__(self, config: Dict[str, Any]):
        """
        Initialize markdown parser with configuration.

        Args:
            config: Parser configuration
        """
        self.config = config
        self.epub_config = config.get("epub", {})
        self.chapter_level = self.epub_config.get("chapter_level", 2)

        logger.debug(
            f"MarkdownParser initialized with chapter_level: {self.chapter_level}"
        )

    def parse_markdown_file(self, markdown_file: str) -> Optional[ParsedMarkdownData]:
        """
        Parse markdown file and extract structure.

        Args:
            markdown_file: Path to markdown file

        Returns:
            Parsed markdown data or None if failed
        """
        try:
            markdown_path = Path(markdown_file)
            if not markdown_path.exists():
                logger.error(f"Markdown file not found: {markdown_file}")
                return None

            # Read file content
            with open(markdown_path, "r", encoding="utf-8") as f:
                content = f.read()

            # Parse YAML frontmatter
            metadata = self._extract_yaml_frontmatter(content)
            if not metadata:
                logger.error("Failed to extract YAML frontmatter")
                return None

            # Remove YAML frontmatter from content
            content_without_yaml = self._remove_yaml_frontmatter(content)

            # Split content into sections
            sections = self._split_content_sections(content_without_yaml)

            # Extract chapters
            chapters = self._extract_chapters(sections.get("chapters", ""))

            if not chapters:
                logger.warning("No chapters found in markdown file")

            parsed_data = ParsedMarkdownData(
                metadata=metadata,
                chapters=chapters,
                title_page_content=sections.get("title_page"),
                toc_content=sections.get("toc"),
            )

            logger.info(
                f"Successfully parsed markdown file: {len(chapters)} chapters found"
            )
            return parsed_data

        except Exception as e:
            logger.error(f"Error parsing markdown file: {e}")
            return None

    def _extract_yaml_frontmatter(self, content: str) -> Optional[Dict[str, Any]]:
        """
        Extract YAML frontmatter from markdown content.

        Args:
            content: Full markdown content

        Returns:
            Parsed YAML metadata or None if failed
        """
        try:
            # Look for YAML frontmatter between --- markers
            yaml_pattern = r"^---\s*\n(.*?)\n---\s*\n"
            match = re.match(yaml_pattern, content, re.DOTALL)

            if not match:
                logger.error("No YAML frontmatter found")
                return None

            yaml_content = match.group(1)
            metadata = yaml.safe_load(yaml_content)

            if not isinstance(metadata, dict):
                logger.error("YAML frontmatter is not a dictionary")
                return None

            logger.debug(f"Extracted metadata keys: {list(metadata.keys())}")
            return metadata

        except yaml.YAMLError as e:
            logger.error(f"Error parsing YAML frontmatter: {e}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error extracting YAML: {e}")
            return None

    def _remove_yaml_frontmatter(self, content: str) -> str:
        """
        Remove YAML frontmatter from content.

        Args:
            content: Full markdown content

        Returns:
            Content without YAML frontmatter
        """
        yaml_pattern = r"^---\s*\n.*?\n---\s*\n"
        return re.sub(yaml_pattern, "", content, flags=re.DOTALL)

    def _split_content_sections(self, content: str) -> Dict[str, str]:
        """
        Split content into title page, TOC, and chapters sections.

        Args:
            content: Markdown content without YAML frontmatter

        Returns:
            Dictionary with content sections
        """
        sections = {}

        # Look for main title (H1)
        title_match = re.search(r"^# (.+?)$", content, re.MULTILINE)
        if title_match:
            title_start = title_match.start()

            # Look for Table of Contents
            toc_match = re.search(r"^# Table of Contents\s*$", content, re.MULTILINE)
            if toc_match:
                toc_start = toc_match.start()
                sections["title_page"] = content[title_start:toc_start].strip()

                # Find where chapters start (first H2 after TOC)
                chapters_match = re.search(r"^## ", content[toc_start:], re.MULTILINE)
                if chapters_match:
                    chapters_start = toc_start + chapters_match.start()
                    sections["toc"] = content[toc_start:chapters_start].strip()
                    sections["chapters"] = content[chapters_start:].strip()
                else:
                    sections["toc"] = content[toc_start:].strip()
                    sections["chapters"] = ""
            else:
                # No TOC found, look for first chapter
                chapters_match = re.search(r"^## ", content[title_start:], re.MULTILINE)
                if chapters_match:
                    chapters_start = title_start + chapters_match.start()
                    sections["title_page"] = content[title_start:chapters_start].strip()
                    sections["chapters"] = content[chapters_start:].strip()
                else:
                    sections["title_page"] = content[title_start:].strip()
                    sections["chapters"] = ""
        else:
            # No main title found, assume everything is chapters
            sections["chapters"] = content.strip()

        return sections

    def _extract_chapters(self, chapters_content: str) -> List[ParsedChapter]:
        """
        Extract individual chapters from chapters content.

        Args:
            chapters_content: Content containing all chapters

        Returns:
            List of parsed chapters
        """
        chapters = []

        if not chapters_content.strip():
            return chapters

        # Split by chapter headings (H2)
        chapter_pattern = r"^## (.+?)(?=\{#(.+?)\})?$"
        chapter_splits = re.split(r"^## ", chapters_content, flags=re.MULTILINE)

        # First split is usually empty or content before first chapter
        if chapter_splits and not chapter_splits[0].strip():
            chapter_splits = chapter_splits[1:]

        chapter_number = 1
        for chapter_text in chapter_splits:
            if not chapter_text.strip():
                continue

            # Extract title and anchor from first line
            lines = chapter_text.split("\n", 1)
            if not lines:
                continue

            title_line = lines[0].strip()
            content = lines[1] if len(lines) > 1 else ""

            # Parse title and anchor
            title, anchor = self._parse_chapter_title_and_anchor(title_line)

            if not title:
                logger.warning(f"Skipping chapter with empty title: {title_line}")
                continue

            # Debug logging for problematic chapters
            if title in ["Chapter 1.1", "Chapter 9.1", "Chapter 15.2"]:
                logger.info(
                    f"DEBUG PARSER: {title} - Raw content length: {len(content)}"
                )
                logger.info(
                    f"DEBUG PARSER: {title} - Raw content (first 200 chars): {repr(content[:200])}"
                )

            # Clean up content
            content = self._clean_chapter_content(content, title)

            # Debug logging after cleaning
            if title in ["Chapter 1.1", "Chapter 9.1", "Chapter 15.2"]:
                logger.info(
                    f"DEBUG PARSER: {title} - Cleaned content length: {len(content)}"
                )
                logger.info(
                    f"DEBUG PARSER: {title} - Cleaned content (first 200 chars): {repr(content[:200])}"
                )

            chapter = ParsedChapter(
                title=title,
                content=content,
                anchor=anchor or f"chapter-{chapter_number}",
                chapter_number=chapter_number,
            )

            chapters.append(chapter)
            chapter_number += 1

        logger.debug(f"Extracted {len(chapters)} chapters")
        return chapters

    def _parse_chapter_title_and_anchor(
        self, title_line: str
    ) -> tuple[str, Optional[str]]:
        """
        Parse chapter title and anchor from title line.

        Args:
            title_line: Chapter title line (may include anchor)

        Returns:
            Tuple of (title, anchor)
        """
        # Look for anchor pattern: Title {#anchor}
        anchor_pattern = r"^(.+?)\s*\{#(.+?)\}\s*$"
        match = re.match(anchor_pattern, title_line)

        if match:
            title = match.group(1).strip()
            anchor = match.group(2).strip()
            return title, anchor
        else:
            # No anchor found, use title as-is
            return title_line.strip(), None

    def _clean_chapter_content(self, content: str, chapter_title: str = "") -> str:
        """
        Clean and format chapter content.

        Args:
            content: Raw chapter content
            chapter_title: Chapter title to remove duplicates from content

        Returns:
            Cleaned chapter content
        """
        if not content:
            return ""

        # Remove duplicate chapter titles from content
        content = self._remove_duplicate_chapter_titles(content, chapter_title)

        # Remove advertisement content
        content = self._remove_advertisement_content(content)

        # Remove Pandoc-specific markup
        content = self._remove_pandoc_markup(content)

        # Remove excessive whitespace
        content = re.sub(r"\n\s*\n\s*\n", "\n\n", content)

        # Remove trailing whitespace from lines
        lines = [line.rstrip() for line in content.split("\n")]
        content = "\n".join(lines)

        # Remove leading/trailing whitespace
        content = content.strip()

        return content

    def _remove_duplicate_chapter_titles(self, content: str, chapter_title: str) -> str:
        """
        Remove duplicate chapter titles that appear in the content.

        Args:
            content: Chapter content that may contain duplicate titles
            chapter_title: The chapter title to look for and remove

        Returns:
            Content with duplicate chapter titles removed
        """
        if not content or not chapter_title:
            return content

        # Split content into paragraphs
        paragraphs = content.split("\n\n")
        cleaned_paragraphs = []

        # Extract the core title from the chapter title for matching
        core_title = self._extract_core_title(chapter_title)

        for paragraph in paragraphs:
            # Skip empty paragraphs
            if not paragraph.strip():
                continue

            paragraph_text = paragraph.strip()

            # Check if this paragraph is a duplicate chapter title
            if self._is_duplicate_chapter_title(
                paragraph_text, chapter_title, core_title
            ):
                logger.debug(
                    f"Removing duplicate chapter title: {paragraph_text[:50]}..."
                )
                continue  # Skip this paragraph

            cleaned_paragraphs.append(paragraph)

        return "\n\n".join(cleaned_paragraphs)

    def _extract_core_title(self, chapter_title: str) -> str:
        """
        Extract the core title from a chapter title, removing chapter numbers and prefixes.

        Args:
            chapter_title: Full chapter title

        Returns:
            Core title without chapter numbering
        """
        if not chapter_title:
            return ""

        # Remove common chapter prefixes and numbering
        import re

        # Patterns to remove:
        # - "Chapter 246: "
        # - "246. "
        # - "Chapter 246 - 246."
        patterns = [
            r"^Chapter\s+\d+\s*:\s*",  # "Chapter 246: "
            r"^Chapter\s+\d+\s*-\s*\d+\s*[.:]?\s*",  # "Chapter 246 - 246."
            r"^\d+\s*[.:]?\s*",  # "246. " or "246: "
        ]

        core_title = chapter_title
        for pattern in patterns:
            core_title = re.sub(pattern, "", core_title, flags=re.IGNORECASE).strip()

        return core_title

    def _is_duplicate_chapter_title(
        self, paragraph: str, full_title: str, core_title: str
    ) -> bool:
        """
        Check if a paragraph is a duplicate of the chapter title.

        Args:
            paragraph: Paragraph text to check
            full_title: Full chapter title
            core_title: Core title without numbering

        Returns:
            True if paragraph is a duplicate chapter title
        """
        if not paragraph or not core_title:
            return False

        paragraph_lower = paragraph.lower().strip()
        full_title_lower = full_title.lower().strip()
        core_title_lower = core_title.lower().strip()

        # Direct matches
        if paragraph_lower == full_title_lower:
            return True

        if paragraph_lower == core_title_lower:
            return True

        # Check for patterns like "246\.Are You Taking the Beast Tamer Examination?"
        import re

        # Remove escaped characters and check again
        cleaned_paragraph = re.sub(r"\\(.)", r"\1", paragraph_lower)
        if cleaned_paragraph == core_title_lower:
            return True

        # Check for numbered patterns at the start
        # Pattern: "246.Are You Taking..." or "246\.Are You Taking..."
        number_pattern = r"^\d+\\?\.\s*"
        if re.match(number_pattern, paragraph):
            # Remove the number prefix and check if it matches core title
            cleaned = re.sub(number_pattern, "", paragraph, flags=re.IGNORECASE).strip()
            if cleaned.lower() == core_title_lower:
                return True

        # Check for very similar content (high similarity)
        # If the paragraph is mostly the same as the title, it's likely a duplicate
        if len(core_title_lower) > 10:  # Only for substantial titles
            # Simple similarity check: if core title is contained in paragraph or vice versa
            if (
                core_title_lower in paragraph_lower
                or paragraph_lower in core_title_lower
            ):
                # Additional check: ensure they're similar enough in length
                len_ratio = min(len(paragraph_lower), len(core_title_lower)) / max(
                    len(paragraph_lower), len(core_title_lower)
                )
                if len_ratio > 0.7:  # 70% length similarity
                    return True

        return False

    def _remove_advertisement_content(self, content: str) -> str:
        """
        Remove advertisement and promotional content from chapter text.

        Args:
            content: Chapter content that may contain ads

        Returns:
            Content with advertisement paragraphs removed
        """
        if not content:
            return ""

        # Split content into paragraphs
        paragraphs = content.split("\n\n")
        cleaned_paragraphs = []

        for paragraph in paragraphs:
            # Skip empty paragraphs
            if not paragraph.strip():
                continue

            # Convert to lowercase for pattern matching (preserve original case)
            paragraph_lower = paragraph.lower().strip()

            # Define advertisement patterns to remove
            # NOTE: These patterns should be specific to avoid false positives with story content
            ad_patterns = [
                # Report chapter/error messages (specific combinations)
                "if you find any errors",
                "ads popup",
                "ads redirect",
                "broken links",
                "non-standard content",
                "report chapter",
                "let us know",
                "fix it as soon as possible",
                # More specific combinations to avoid false positives
                "< report chapter >",
                "&lt; report chapter &gt;",
                # Common ad phrases (specific phrases only)
                "advertisement",
                "sponsored content",
                "click here to",
                "visit our website",
                "subscribe to",
                "follow us on",
                "like us on",
                "share this story",
                "download our app",
                "mobile app download",
                # Promotional content (specific phrases)
                "special offer",
                "limited time offer",
                "discount code",
                "free trial",
                "premium membership",
                "vip membership",
                "support us on patreon",
                "donate to",
                "patreon.com",
                "paypal.me",
                # Navigation elements (specific phrases only)
                "home page",
                "novel list page",
                "bookmark this",
                "add to library",
                "table of contents",
            ]

            # Check if paragraph contains advertisement content
            is_ad = False

            # High-confidence ad patterns (single match is enough)
            high_confidence_patterns = [
                "if you find any errors",
                "ads popup",
                "ads redirect",
                "< report chapter >",
                "&lt; report chapter &gt;",
                "advertisement",
                "sponsored",
                "click here",
                "visit our website",
                "download app",
                "special offer",
                "limited time",
            ]

            # Check high-confidence patterns first
            for pattern in high_confidence_patterns:
                if pattern in paragraph_lower:
                    is_ad = True
                    break

            # For other patterns, require multiple indicators
            if not is_ad:
                ad_indicators = 0
                matched_patterns = []
                for pattern in ad_patterns:
                    if pattern in paragraph_lower:
                        ad_indicators += 1
                        matched_patterns.append(pattern)
                        if ad_indicators >= 2:  # Require at least 2 indicators
                            is_ad = True
                            break

            # Additional checks for specific ad patterns
            if not is_ad:
                # Check for HTML entities that often appear in ads
                if "&lt;" in paragraph and "&gt;" in paragraph:
                    is_ad = True

                # Check for very short paragraphs that are likely navigation
                if len(paragraph.strip()) < 10:
                    is_ad = True

                # Check for paragraphs that are mostly punctuation/symbols
                text_chars = sum(1 for c in paragraph if c.isalnum())
                total_chars = len(paragraph.strip())
                if total_chars > 0 and text_chars / total_chars < 0.5:
                    is_ad = True

            # Only keep non-advertisement paragraphs
            if not is_ad:
                cleaned_paragraphs.append(paragraph)

        return "\n\n".join(cleaned_paragraphs)

    def _remove_pandoc_markup(self, content: str) -> str:
        """
        Remove Pandoc-specific markup that shouldn't appear in EbookLib EPUB.

        Args:
            content: Content with potential Pandoc markup

        Returns:
            Cleaned content without Pandoc markup
        """
        # Remove Pandoc div blocks: ::: {.class} and :::
        content = re.sub(r":::\s*\{[^}]*\}\s*\n?", "", content)
        content = re.sub(r":::\s*\n?", "", content)

        # Remove \newpage commands (both single and double backslash variants)
        content = re.sub(r"\\\\?newpage\s*\n?", "", content)

        # Remove Pandoc-style page breaks
        content = re.sub(r"\\\\?pagebreak\s*\n?", "", content)

        # Remove Pandoc-style line breaks
        content = re.sub(r"\\\\\\\\\s*\n?", "\n", content)

        # Remove Pandoc metadata blocks that might have leaked through
        content = re.sub(
            r"^---\s*\n.*?\n---\s*\n", "", content, flags=re.MULTILINE | re.DOTALL
        )

        # Remove empty lines that might have been left behind
        content = re.sub(r"\n\s*\n\s*\n", "\n\n", content)

        return content
