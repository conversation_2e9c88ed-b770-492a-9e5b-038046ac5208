"""
Image processing module for cover images.

This module handles downloading, resizing, and processing cover images for novels.
"""

import asyncio
import io
import logging
from pathlib import Path
from typing import Any, Dict, Optional, Tuple
from urllib.parse import urlparse

import aiohttp
import cloudscraper
from PIL import Image, ImageDraw, ImageFont

from ..utils import clean_filename, create_safe_directory

logger = logging.getLogger(__name__)


class ImageProcessor:
    """
    Handles image processing operations for novel covers.

    Provides functionality to download, resize, optimize, and create placeholder images.
    """

    def __init__(self, config: Dict[str, Any]):
        """
        Initialize image processor with configuration.

        Args:
            config: Image processing configuration
        """
        self.config = config.get("images", {})
        self.target_size = tuple(self.config.get("target_size", [600, 800]))
        self.quality = self.config.get("quality", 85)
        self.format = self.config.get("format", "JPEG")
        self.create_placeholder = self.config.get("create_placeholder", True)
        self.placeholder_color = self.config.get("placeholder_color", "#2c3e50")
        self.placeholder_text_color = self.config.get(
            "placeholder_text_color", "#ecf0f1"
        )

        # Create cloudscraper session for image downloads
        self.session = cloudscraper.create_scraper()

        logger.debug(f"ImageProcessor initialized with target_size: {self.target_size}")

    async def download_and_process_cover(
        self,
        cover_url: str,
        output_dir: Path,
        novel_title: str,
        filename: Optional[str] = None,
    ) -> Optional[str]:
        """
        Download and process a cover image.

        Args:
            cover_url: URL of the cover image
            output_dir: Directory to save the processed image
            novel_title: Title of the novel (for filename and placeholder)
            filename: Optional custom filename

        Returns:
            Path to the processed image file or None if failed
        """
        if not cover_url:
            logger.warning("No cover URL provided")
            if self.create_placeholder:
                return await self.create_placeholder_cover(
                    output_dir, novel_title, filename
                )
            return None

        try:
            # Create output directory
            if not create_safe_directory(output_dir):
                logger.error(f"Failed to create output directory: {output_dir}")
                return None

            # Generate filename
            if not filename:
                filename = self._generate_filename(novel_title, cover_url)

            output_path = output_dir / filename

            # Download image
            image_data = await self._download_image(cover_url)
            if not image_data:
                logger.warning(f"Failed to download cover image from {cover_url}")
                if self.create_placeholder:
                    return await self.create_placeholder_cover(
                        output_dir, novel_title, filename
                    )
                return None

            # Process image
            processed_path = await self._process_image(image_data, output_path)
            if processed_path:
                logger.info(f"Successfully processed cover image: {processed_path}")
                return str(processed_path)
            else:
                logger.warning("Failed to process cover image")
                if self.create_placeholder:
                    return await self.create_placeholder_cover(
                        output_dir, novel_title, filename
                    )
                return None

        except Exception as e:
            logger.error(f"Error processing cover image: {e}")
            if self.create_placeholder:
                return await self.create_placeholder_cover(
                    output_dir, novel_title, filename
                )
            return None

    async def create_placeholder_cover(
        self, output_dir: Path, novel_title: str, filename: Optional[str] = None
    ) -> Optional[str]:
        """
        Create a placeholder cover image.

        Args:
            output_dir: Directory to save the placeholder
            novel_title: Title of the novel
            filename: Optional custom filename

        Returns:
            Path to the placeholder image file or None if failed
        """
        try:
            # Create output directory
            if not create_safe_directory(output_dir):
                logger.error(f"Failed to create output directory: {output_dir}")
                return None

            # Generate filename
            if not filename:
                filename = self._generate_filename(novel_title, None, "placeholder")

            output_path = output_dir / filename

            # Create placeholder image
            image = Image.new("RGB", self.target_size, self.placeholder_color)
            draw = ImageDraw.Draw(image)

            # Add title text
            self._add_text_to_image(draw, novel_title, image.size)

            # Save image
            image.save(output_path, self.format, quality=self.quality, optimize=True)

            logger.info(f"Created placeholder cover: {output_path}")
            return str(output_path)

        except Exception as e:
            logger.error(f"Error creating placeholder cover: {e}")
            return None

    async def _download_image(self, url: str) -> Optional[bytes]:
        """
        Download image from URL.

        Args:
            url: Image URL

        Returns:
            Image data as bytes or None if failed
        """
        try:
            logger.debug(f"Downloading image from: {url}")

            # Use cloudscraper for downloads
            response = self.session.get(url, timeout=30)

            if response.status_code == 200:
                return response.content
            else:
                logger.error(f"Failed to download image: HTTP {response.status_code}")
                return None

        except Exception as e:
            logger.error(f"Error downloading image from {url}: {e}")
            return None

    async def _process_image(
        self, image_data: bytes, output_path: Path
    ) -> Optional[Path]:
        """
        Process image data and save to file.

        Args:
            image_data: Raw image data
            output_path: Path to save processed image

        Returns:
            Path to processed image or None if failed
        """
        try:
            # Open image
            image = Image.open(io.BytesIO(image_data))

            # Convert to RGB if necessary
            if image.mode != "RGB":
                image = image.convert("RGB")

            # Resize image
            image = self._resize_image(image)

            # Save processed image
            image.save(output_path, self.format, quality=self.quality, optimize=True)

            return output_path

        except Exception as e:
            logger.error(f"Error processing image: {e}")
            return None

    def _resize_image(self, image: Image.Image) -> Image.Image:
        """
        Resize image to target size while maintaining aspect ratio.

        Args:
            image: PIL Image object

        Returns:
            Resized image
        """
        # Calculate aspect ratios
        original_ratio = image.width / image.height
        target_ratio = self.target_size[0] / self.target_size[1]

        if original_ratio > target_ratio:
            # Image is wider than target ratio
            new_width = self.target_size[0]
            new_height = int(new_width / original_ratio)
        else:
            # Image is taller than target ratio
            new_height = self.target_size[1]
            new_width = int(new_height * original_ratio)

        # Resize image
        resized = image.resize((new_width, new_height), Image.Resampling.LANCZOS)

        # Create final image with target size and center the resized image
        final_image = Image.new("RGB", self.target_size, (255, 255, 255))

        # Calculate position to center the image
        x = (self.target_size[0] - new_width) // 2
        y = (self.target_size[1] - new_height) // 2

        final_image.paste(resized, (x, y))

        return final_image

    def _add_text_to_image(
        self, draw: ImageDraw.Draw, text: str, image_size: Tuple[int, int]
    ) -> None:
        """
        Add text to image for placeholder covers.

        Args:
            draw: ImageDraw object
            text: Text to add
            image_size: Size of the image
        """
        try:
            # Try to use a nice font, fall back to default
            try:
                font_size = min(image_size) // 20
                font = ImageFont.truetype("arial.ttf", font_size)
            except (OSError, IOError):
                try:
                    font = ImageFont.load_default()
                except:
                    font = None

            if font is None:
                return

            # Wrap text to fit image width
            wrapped_text = self._wrap_text(text, font, image_size[0] - 40)

            # Calculate text position (centered)
            bbox = draw.multiline_textbbox((0, 0), wrapped_text, font=font)
            text_width = bbox[2] - bbox[0]
            text_height = bbox[3] - bbox[1]

            x = (image_size[0] - text_width) // 2
            y = (image_size[1] - text_height) // 2

            # Draw text
            draw.multiline_text(
                (x, y),
                wrapped_text,
                fill=self.placeholder_text_color,
                font=font,
                align="center",
            )

        except Exception as e:
            logger.debug(f"Error adding text to image: {e}")

    def _wrap_text(self, text: str, font: ImageFont.ImageFont, max_width: int) -> str:
        """
        Wrap text to fit within specified width.

        Args:
            text: Text to wrap
            font: Font to use for measuring
            max_width: Maximum width in pixels

        Returns:
            Wrapped text with newlines
        """
        words = text.split()
        lines = []
        current_line = []

        for word in words:
            test_line = " ".join(current_line + [word])
            bbox = font.getbbox(test_line)
            width = bbox[2] - bbox[0]

            if width <= max_width:
                current_line.append(word)
            else:
                if current_line:
                    lines.append(" ".join(current_line))
                    current_line = [word]
                else:
                    # Word is too long, add it anyway
                    lines.append(word)

        if current_line:
            lines.append(" ".join(current_line))

        return "\n".join(lines)

    def _generate_filename(
        self, novel_title: str, url: Optional[str] = None, suffix: str = ""
    ) -> str:
        """
        Generate Unix-safe filename for cover image using underscores.

        Args:
            novel_title: Title of the novel
            url: Optional URL to extract extension from
            suffix: Optional suffix to add

        Returns:
            Generated filename with Unix-safe naming
        """
        # Clean title for filename with Unix-safe underscores
        base_name = clean_filename(novel_title, use_underscores=True)

        # Add suffix if provided
        if suffix:
            base_name += f"_{suffix}"

        # Determine extension
        extension = f".{self.format.lower()}"
        if url:
            parsed_url = urlparse(url)
            url_ext = Path(parsed_url.path).suffix.lower()
            if url_ext in [".jpg", ".jpeg", ".png", ".webp", ".gif"]:
                extension = url_ext if self.format.upper() == "ORIGINAL" else extension

        return f"{base_name}_cover{extension}"

    def close(self):
        """Close the session."""
        if self.session:
            self.session.close()
